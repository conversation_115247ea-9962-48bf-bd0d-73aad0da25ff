#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试本机设备过滤功能
"""

from scapy.all import get_if_list, get_if_addr, get_if_hwaddr, AR<PERSON>, <PERSON>ther, srp

def test_local_device_detection():
    """测试本机设备检测"""
    print("=== 本机设备检测测试 ===")
    
    interfaces = get_if_list()
    
    for iface in interfaces:
        try:
            ip = get_if_addr(iface)
            mac = get_if_hwaddr(iface)
            
            if ip and ip != "0.0.0.0" and not ip.startswith("127.") and not ip.startswith("169.254."):
                print(f"\n接口: {iface}")
                print(f"本机IP: {ip}")
                print(f"本机MAC: {mac}")
                
                # 测试扫描网络
                network_range = ".".join(ip.split(".")[:-1]) + ".0/24"
                print(f"扫描网络: {network_range}")
                
                try:
                    arp_request = ARP(pdst=network_range)
                    broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
                    arp_request_broadcast = broadcast / arp_request
                    
                    answered_list = srp(arp_request_broadcast, timeout=3, verbose=False, iface=iface)[0]
                    
                    print(f"发现 {len(answered_list)} 个设备:")
                    
                    local_found = False
                    other_devices = []
                    
                    for element in answered_list:
                        device_ip = element[1].psrc
                        device_mac = element[1].hwsrc
                        
                        # 检查是否为本机
                        is_local = (device_ip == ip) or (device_mac.upper() == mac.upper())
                        
                        if is_local:
                            print(f"  ✅ 本机设备: {device_ip} - {device_mac} (将被过滤)")
                            local_found = True
                        else:
                            print(f"  📱 其他设备: {device_ip} - {device_mac}")
                            other_devices.append((device_ip, device_mac))
                    
                    print(f"\n过滤结果:")
                    print(f"  本机设备: {'已检测到' if local_found else '未检测到'}")
                    print(f"  其他设备: {len(other_devices)} 个")
                    
                    if not local_found:
                        print("  ⚠️  警告: 未在扫描结果中发现本机设备")
                    
                except Exception as e:
                    print(f"扫描失败: {e}")
                
                break  # 只测试第一个有效接口
                
        except Exception as e:
            continue

def is_local_device(ip, mac, local_ip, local_mac):
    """判断是否为本机设备（复制主程序逻辑）"""
    # 检查IP地址
    if local_ip and ip == local_ip:
        return True
    
    # 检查MAC地址
    if local_mac and mac.upper() == local_mac.upper():
        return True
    
    # 检查是否为回环地址
    if ip.startswith("127."):
        return True
    
    return False

def test_filter_logic():
    """测试过滤逻辑"""
    print("\n=== 过滤逻辑测试 ===")
    
    # 测试用例
    test_cases = [
        ("*************", "aa:bb:cc:dd:ee:ff", "*************", "aa:bb:cc:dd:ee:ff", True, "相同IP和MAC"),
        ("*************", "aa:bb:cc:dd:ee:ff", "*************", "11:22:33:44:55:66", True, "相同IP不同MAC"),
        ("*************", "aa:bb:cc:dd:ee:ff", "*************", "aa:bb:cc:dd:ee:ff", True, "不同IP相同MAC"),
        ("*************", "11:22:33:44:55:66", "*************", "aa:bb:cc:dd:ee:ff", False, "完全不同"),
        ("127.0.0.1", "11:22:33:44:55:66", "*************", "aa:bb:cc:dd:ee:ff", True, "回环地址"),
    ]
    
    for device_ip, device_mac, local_ip, local_mac, expected, description in test_cases:
        result = is_local_device(device_ip, device_mac, local_ip, local_mac)
        status = "✅ 通过" if result == expected else "❌ 失败"
        print(f"{status} {description}: {device_ip} - 预期: {expected}, 实际: {result}")

if __name__ == "__main__":
    test_local_device_detection()
    test_filter_logic()
