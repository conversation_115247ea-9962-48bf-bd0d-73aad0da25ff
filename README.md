# Mac设备局域网断网工具

一个基于Python的GUI图形化工具，用于在局域网中识别和管理Mac设备的网络连接。

## ⚠️ 重要警告

**此工具仅用于合法的网络管理目的！**

- 请确保您有权限管理目标设备
- 仅在您拥有或管理的网络中使用
- 恶意使用此工具可能违法，后果自负
- 使用前请了解相关法律法规

## 功能特性

- 🔍 **设备扫描**: 自动扫描局域网中的所有设备
- 🍎 **Mac设备识别**: 智能识别Apple设备（基于MAC地址OUI）
- 🚫 **一键断网**: 通过ARP欺骗技术断开目标设备网络连接
- 🔄 **网络恢复**: 停止攻击，恢复设备正常网络连接
- 📊 **实时监控**: 显示设备状态和操作日志
- 🖥️ **友好界面**: 直观的图形用户界面

## 技术原理

本工具使用ARP欺骗（ARP Spoofing）技术：

1. **ARP协议**: 地址解析协议，用于将IP地址解析为MAC地址
2. **ARP欺骗**: 发送虚假ARP响应，欺骗目标设备和网关
3. **中间人攻击**: 将自己伪装成网关，拦截目标设备的网络流量
4. **断网效果**: 目标设备无法正常访问网络

## 系统要求

- Python 3.6+
- Windows/Linux/macOS
- 管理员权限（必需）

## 安装步骤

### 1. 克隆或下载项目

```bash
git clone <repository-url>
cd mac-disconnect-tool
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 以管理员权限运行

**Windows:**
```bash
# 以管理员身份打开命令提示符
python mac_disconnect_tool.py
```

**Linux/macOS:**
```bash
sudo python3 mac_disconnect_tool.py
```

## 使用方法

### 1. 启动程序

运行程序后会显示图形界面，首次启动会显示使用警告。

### 2. 选择网络接口

- 在界面顶部选择要使用的网络接口
- 点击"刷新接口"按钮更新接口列表

### 3. 扫描设备

- 点击"扫描设备"按钮开始扫描局域网
- 扫描完成后会在列表中显示所有发现的设备
- Mac设备会被特别标识

### 4. 断开设备

- 在设备列表中选择要断网的设备
- 点击"断开选中设备"按钮
- 确认操作后开始ARP攻击

### 5. 恢复连接

- 选择已断网的设备
- 点击"恢复选中设备"按钮停止攻击
- 或点击"停止所有攻击"按钮恢复所有设备

## 界面说明

### 主要组件

- **网络接口选择**: 选择用于扫描和攻击的网络接口
- **设备列表**: 显示扫描到的设备信息
  - IP地址: 设备的IP地址
  - MAC地址: 设备的物理地址
  - 厂商: 根据MAC地址识别的设备厂商
  - 状态: 设备当前状态（正常/已断网）
- **操作按钮**: 执行扫描、断网、恢复等操作
- **操作日志**: 显示程序运行日志和状态信息

### 操作提示

- 双击设备可查看详细信息
- 支持多选设备进行批量操作
- 实时显示攻击状态和日志

## 注意事项

### 安全考虑

1. **权限要求**: 必须以管理员权限运行
2. **网络影响**: 攻击会影响目标设备的网络使用
3. **检测风险**: 可能被网络安全设备检测到
4. **法律风险**: 未经授权使用可能违法

### 使用限制

1. **仅限内网**: 只能在同一局域网内使用
2. **设备识别**: 基于MAC地址OUI，可能存在误判
3. **网络环境**: 某些网络配置可能影响效果
4. **防护机制**: 现代设备可能有ARP防护

### 故障排除

**常见问题:**

1. **权限不足**: 确保以管理员身份运行
2. **扫描失败**: 检查网络接口选择是否正确
3. **攻击无效**: 确认目标设备在同一网段
4. **依赖缺失**: 运行 `pip install -r requirements.txt`

## 免责声明

本工具仅供学习和合法的网络管理使用。开发者不对任何滥用行为承担责任。使用者应当：

- 遵守当地法律法规
- 获得网络管理权限
- 承担使用风险和后果
- 不用于恶意攻击

## 许可证

本项目仅供教育和合法网络管理目的使用。

## 技术支持

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发送邮件
- 技术讨论

---

**再次提醒：请合法合规使用此工具！**
