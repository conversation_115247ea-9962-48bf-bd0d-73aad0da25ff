#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能：本机设备红色显示和一键断网所有设备
"""

import tkinter as tk
from tkinter import ttk

def create_test_gui():
    """创建测试GUI来演示新功能"""
    root = tk.Tk()
    root.title("新功能测试 - 设备列表显示")
    root.geometry("800x400")
    
    # 创建设备列表
    columns = ('IP地址', 'MAC地址', '厂商', '状态', '操作')
    tree = ttk.Treeview(root, columns=columns, show='headings', height=15)
    
    # 设置列标题和宽度
    for col in columns:
        tree.heading(col, text=col)
        if col == 'IP地址':
            tree.column(col, width=120)
        elif col == 'MAC地址':
            tree.column(col, width=140)
        elif col == '厂商':
            tree.column(col, width=200)
        elif col == '状态':
            tree.column(col, width=120)
        else:
            tree.column(col, width=100)
    
    # 配置本机设备的红色标签样式
    tree.tag_configure('local_device', background='#ffebee', foreground='#d32f2f')
    
    # 添加测试数据
    test_devices = [
        ("************", "f0:b4:29:87:44:fb", "路由器厂商", "其他设备", ""),
        ("**************", "6e:10:22:5d:c6:91", "未知厂商", "其他设备", ""),
        ("************58", "a4:83:e7:2e:8f:1c", "Apple", "Mac设备", ""),
        ("**************", "f8:3d:c6:41:dd:f4", "Intel", "🔴 本机设备", ""),  # 本机设备
        ("**************", "b8:27:eb:a1:b2:c3", "Raspberry Pi", "其他设备", ""),
    ]
    
    for i, device in enumerate(test_devices):
        item = tree.insert('', tk.END, values=device)
        
        # 为本机设备设置红色标签
        if "🔴 本机设备" in device[3]:
            tree.item(item, tags=('local_device',))
    
    tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 添加说明标签
    info_label = tk.Label(root, 
                         text="🔴 红色背景表示本机设备，不会被攻击\n"
                              "双击设备可查看详细信息\n"
                              "本机设备在攻击时会被自动跳过",
                         fg='#d32f2f', font=('Arial', 10))
    info_label.pack(pady=5)
    
    # 添加按钮测试区域
    button_frame = ttk.Frame(root)
    button_frame.pack(pady=10)
    
    def test_select_all():
        """测试选择所有非本机设备"""
        tree.selection_remove(tree.selection())
        for item in tree.get_children():
            values = tree.item(item)['values']
            if "🔴 本机设备" not in values[3]:
                tree.selection_add(item)
        print(f"选择了 {len(tree.selection())} 个非本机设备")
    
    def test_select_mac():
        """测试选择Mac设备"""
        tree.selection_remove(tree.selection())
        for item in tree.get_children():
            values = tree.item(item)['values']
            if "Mac设备" in values[3] and "🔴 本机设备" not in values[3]:
                tree.selection_add(item)
        print(f"选择了 {len(tree.selection())} 个Mac设备")
    
    def show_selection():
        """显示当前选择"""
        selected = tree.selection()
        print(f"\n当前选择了 {len(selected)} 个设备:")
        for item in selected:
            values = tree.item(item)['values']
            print(f"  {values[0]} - {values[3]}")
    
    ttk.Button(button_frame, text="选择全部非本机", command=test_select_all).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="只选Mac设备", command=test_select_mac).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="显示选择", command=show_selection).pack(side=tk.LEFT, padx=5)
    
    # 双击事件处理
    def on_double_click(event):
        selection = tree.selection()
        if selection:
            values = tree.item(selection[0])['values']
            ip = values[0]
            status = values[3]
            
            if "🔴 本机设备" in status:
                tk.messagebox.showinfo("本机设备", 
                                     f"🔴 这是您的本机设备！\n\n"
                                     f"IP: {ip}\n"
                                     f"⚠️ 本机设备受到保护，不会被攻击。")
            else:
                tk.messagebox.showinfo("设备信息", f"设备: {ip}\n状态: {status}")
    
    tree.bind('<Double-1>', on_double_click)
    
    root.mainloop()

if __name__ == "__main__":
    print("启动新功能测试GUI...")
    print("功能说明:")
    print("1. 本机设备用红色背景显示")
    print("2. 选择功能自动排除本机设备")
    print("3. 双击本机设备显示保护提示")
    print("4. 一键断网功能自动跳过本机设备")
    
    create_test_gui()
