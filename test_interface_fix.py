#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试接口名称修复
"""

from scapy.all import get_if_list, get_if_addr, ARP, Ether, srp

def test_interface_names():
    """测试接口名称"""
    print("=== 接口名称测试 ===")
    
    interfaces = get_if_list()
    print(f"发现 {len(interfaces)} 个接口:")
    
    for i, iface in enumerate(interfaces):
        try:
            ip = get_if_addr(iface)
            print(f"{i+1}. 接口: {iface}")
            print(f"    IP: {ip}")
            
            if ip and ip != "0.0.0.0" and not ip.startswith("127.") and not ip.startswith("169.254."):
                print(f"    ✅ 有效接口")
                
                # 测试ARP扫描
                try:
                    network_range = ".".join(ip.split(".")[:-1]) + ".1/32"  # 只扫描网关
                    print(f"    测试扫描: {network_range}")
                    
                    arp_request = ARP(pdst=network_range)
                    broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
                    arp_request_broadcast = broadcast / arp_request
                    
                    answered_list = srp(arp_request_broadcast, timeout=2, verbose=False, iface=iface)[0]
                    
                    if answered_list:
                        for element in answered_list:
                            gateway_ip = element[1].psrc
                            gateway_mac = element[1].hwsrc
                            print(f"    网关: {gateway_ip} - {gateway_mac}")
                    else:
                        print(f"    未找到网关")
                        
                except Exception as e:
                    print(f"    扫描失败: {e}")
            else:
                print(f"    ❌ 无效接口")
            
            print()
            
        except Exception as e:
            print(f"{i+1}. 接口: {iface} - 错误: {e}")

if __name__ == "__main__":
    test_interface_names()
