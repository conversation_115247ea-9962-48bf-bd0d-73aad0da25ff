#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络功能测试脚本
用于测试网络接口和扫描功能
"""

import sys
import platform
from scapy.all import get_if_list, get_if_addr, get_if_hwaddr
import netifaces

def test_scapy_interfaces():
    """测试scapy接口功能"""
    print("=== Scapy 接口测试 ===")
    try:
        interfaces = get_if_list()
        print(f"发现 {len(interfaces)} 个接口:")
        
        for i, iface in enumerate(interfaces):
            try:
                ip = get_if_addr(iface)
                mac = get_if_hwaddr(iface)
                print(f"{i+1}. {iface}")
                print(f"   IP: {ip}")
                print(f"   MAC: {mac}")
                print()
            except Exception as e:
                print(f"{i+1}. {iface} - 错误: {e}")
                
    except Exception as e:
        print(f"Scapy接口测试失败: {e}")

def test_netifaces():
    """测试netifaces功能"""
    print("=== Netifaces 接口测试 ===")
    try:
        interfaces = netifaces.interfaces()
        print(f"发现 {len(interfaces)} 个接口:")
        
        for i, iface in enumerate(interfaces):
            try:
                addrs = netifaces.ifaddresses(iface)
                print(f"{i+1}. {iface}")
                
                if netifaces.AF_INET in addrs:
                    ip_info = addrs[netifaces.AF_INET][0]
                    print(f"   IP: {ip_info.get('addr', 'N/A')}")
                    print(f"   Netmask: {ip_info.get('netmask', 'N/A')}")
                
                if netifaces.AF_LINK in addrs:
                    mac_info = addrs[netifaces.AF_LINK][0]
                    print(f"   MAC: {mac_info.get('addr', 'N/A')}")
                
                print()
            except Exception as e:
                print(f"{i+1}. {iface} - 错误: {e}")
                
    except Exception as e:
        print(f"Netifaces测试失败: {e}")

def test_gateway():
    """测试网关获取"""
    print("=== 网关测试 ===")
    try:
        gateways = netifaces.gateways()
        print("网关信息:")
        print(gateways)
        
        default_gateway = gateways.get('default', {})
        if netifaces.AF_INET in default_gateway:
            print(f"默认网关: {default_gateway[netifaces.AF_INET][0]}")
        else:
            print("未找到默认网关")
            
    except Exception as e:
        print(f"网关测试失败: {e}")

def main():
    """主函数"""
    print(f"系统: {platform.system()}")
    print(f"Python版本: {sys.version}")
    print()
    
    test_scapy_interfaces()
    print("\n" + "="*50 + "\n")
    
    test_netifaces()
    print("\n" + "="*50 + "\n")
    
    test_gateway()

if __name__ == "__main__":
    main()
